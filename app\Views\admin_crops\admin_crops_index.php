<?= $this->extend('templates/adminlte/admindash_template') ?>

<?= $this->section('content') ?>

<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0 text-success"><?= $page_header ?></h1>
                <p class="text-muted"><?= $page_desc ?></p>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="<?= base_url() ?>admin/dashboard">Dashboard</a></li>
                    <li class="breadcrumb-item active">Crops Data</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<section class="content">
    <div class="container-fluid">
        
        <!-- Summary Statistics Row -->
        <div class="row mb-4">
            <div class="col-lg-3 col-6">
                <div class="info-box bg-success">
                    <span class="info-box-icon"><i class="fas fa-seedling"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Total Crops</span>
                        <span class="info-box-number"><?= number_format($summary_stats['total_crops']) ?></span>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="info-box bg-info">
                    <span class="info-box-icon"><i class="fas fa-th-large"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Total Blocks</span>
                        <span class="info-box-number"><?= number_format($summary_stats['total_blocks']) ?></span>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="info-box bg-warning">
                    <span class="info-box-icon"><i class="fas fa-users"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Total Farmers</span>
                        <span class="info-box-number"><?= number_format($summary_stats['total_farmers']) ?></span>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-6">
                <div class="info-box bg-danger">
                    <span class="info-box-icon"><i class="fas fa-chart-area"></i></span>
                    <div class="info-box-content">
                        <span class="info-box-text">Total Hectares</span>
                        <span class="info-box-number"><?= number_format($summary_stats['total_hectares'], 2) ?></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Crops Data Table -->
        <div class="row">
            <div class="col-12">
                <div class="card card-success card-outline">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-seedling mr-2"></i>
                            Crops Overview
                        </h3>
                        <div class="card-tools">
                            <button type="button" class="btn btn-tool" data-card-widget="collapse">
                                <i class="fas fa-minus"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table id="crops-table" class="table table-bordered table-striped table-hover">
                                <thead class="bg-success">
                                    <tr>
                                        <th>Crop</th>
                                        <th>Blocks</th>
                                        <th>Farmers</th>
                                        <th>Total Plants</th>
                                        <th>Total Hectares</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($crops_data as $crop): ?>
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <?php if (!empty($crop['crop_icon'])): ?>
                                                    <i class="<?= $crop['crop_icon'] ?> mr-2" 
                                                       style="color: <?= $crop['crop_color_code'] ?? '#28a745' ?>; font-size: 1.2em;"></i>
                                                <?php else: ?>
                                                    <div class="bg-success rounded-circle d-flex align-items-center justify-content-center mr-2" 
                                                         style="width: 25px; height: 25px; background-color: <?= $crop['crop_color_code'] ?? '#28a745' ?> !important;">
                                                        <i class="fas fa-seedling text-white" style="font-size: 0.8em;"></i>
                                                    </div>
                                                <?php endif; ?>
                                                <strong><?= esc($crop['crop_name']) ?></strong>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge badge-info badge-lg">
                                                <?= number_format($crop['blocks_count']) ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge badge-warning badge-lg">
                                                <?= number_format($crop['farmers_count']) ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge badge-primary badge-lg">
                                                <?= number_format($crop['total_plants']) ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge badge-success badge-lg">
                                                <?= number_format($crop['total_hectares'], 2) ?> ha
                                            </span>
                                        </td>
                                        <td>
                                            <a href="<?= base_url('admin/crops/' . $crop['id']) ?>"
                                               class="btn btn-sm btn-success">
                                                <i class="fas fa-eye mr-1"></i>
                                                View Details
                                            </a>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</section>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    $('#crops-table').DataTable({
        "responsive": true,
        "scrollX": true,
        "scrollCollapse": true,
        "autoWidth": false,
        "pageLength": 25,
        "fixedHeader": true,
        "order": [[0, "asc"]],
        "columnDefs": [
            {
                "targets": [1, 2, 3, 4], // Numeric columns
                "className": "text-center"
            },
            {
                "targets": [5], // Actions column
                "orderable": false,
                "className": "text-center"
            }
        ],
        "dom": "<'row'<'col-sm-12 col-md-6'B><'col-sm-12 col-md-6'f>>" +
               "<'row'<'col-sm-12'tr>>" +
               "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
        "buttons": [
            {
                extend: 'excel',
                className: 'btn-sm btn-success',
                text: '<i class="fas fa-file-excel mr-1"></i> Excel',
                title: 'Crops Data Report',
                exportOptions: {
                    columns: [0, 1, 2, 3, 4] // Exclude actions column
                }
            },
            {
                extend: 'pdf',
                className: 'btn-sm btn-danger',
                text: '<i class="fas fa-file-pdf mr-1"></i> PDF',
                title: 'Crops Data Report',
                orientation: 'landscape',
                exportOptions: {
                    columns: [0, 1, 2, 3, 4] // Exclude actions column
                }
            },
            {
                extend: 'print',
                className: 'btn-sm btn-info',
                text: '<i class="fas fa-print mr-1"></i> Print',
                title: 'Crops Data Report',
                exportOptions: {
                    columns: [0, 1, 2, 3, 4] // Exclude actions column
                }
            }
        ]
    }).buttons().container().appendTo('#crops-table_wrapper .col-md-6:eq(0)');

    // Add custom styling to badges
    $('.badge-lg').css({
        'font-size': '0.9em',
        'padding': '0.4em 0.6em'
    });
});
</script>
<?= $this->endSection() ?>
